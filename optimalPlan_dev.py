import json
import cv2
import numpy as np
from pathlib import Path
'''
基于yolov8-1实例分割的mask，实现蘑菇采摘的最佳方案（采摘顺序+采摘方向）
'''

def load_and_draw_masks(image_path, json_path, colors=None):
    """从JSON文件读取轮廓点并绘制到原图上"""
    # 读取原图
    img = cv2.imread(str(image_path))
    if img is None:
        raise ValueError(f"Cannot load image: {image_path}")
    
    # 读取轮廓数据
    with open(json_path, 'r') as f:
        contours_data = json.load(f)
    
    # 创建mask画布
    mask_overlay = img.copy()
    
    # 默认颜色调色板
    if colors is None:
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)
        ]
    
    for i, obj_data in enumerate(contours_data):
        contours = obj_data['contours']
        box = obj_data['box']
        
        # 选择颜色
        color = colors[i % len(colors)]
        
        # 绘制每个轮廓
        for contour in contours:
            if len(contour) > 0:
                # 转换为numpy数组
                contour_np = np.array(contour, dtype=np.int32).reshape(-1, 1, 2)
                
                # 填充mask
                cv2.fillPoly(mask_overlay, [contour_np], color)
                
                # 绘制轮廓边界
                cv2.polylines(img, [contour_np], True, color, 2)
        
        # 绘制边界框（可选）
        if len(box) >= 4:
            x1, y1, x2, y2 = map(int, box[:4])
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
    
    # 混合原图和mask
    result = cv2.addWeighted(img, 0.7, mask_overlay, 0.3, 0)
    
    return result, img, mask_overlay

def create_individual_masks(image_shape, json_path):
    """创建单独的mask数组"""
    with open(json_path, 'r') as f:
        contours_data = json.load(f)
    
    masks = []
    for obj_data in contours_data:
        # 创建单个mask
        mask = np.zeros(image_shape[:2], dtype=np.uint8)
        
        for contour in obj_data['contours']:
            if len(contour) > 0:
                contour_np = np.array(contour, dtype=np.int32).reshape(-1, 1, 2)
                cv2.fillPoly(mask, [contour_np], 255)
        
        masks.append(mask)
    
    return np.array(masks)

def batch_process_masks(image_dir, mask_dir, output_dir):
    """批量处理mask文件
    # 使用示例
    batch_process_masks("images/", "runs/segment/masks/", "output_masked/")
    """
    image_dir = Path(image_dir)
    mask_dir = Path(mask_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for json_file in mask_dir.glob("*_contours.json"):
        # 找到对应的图像文件
        img_stem = json_file.stem.replace("_contours", "")
        
        # 尝试不同的图像格式
        img_path = None
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            candidate = image_dir / f"{img_stem}{ext}"
            if candidate.exists():
                img_path = candidate
                break
        
        if img_path is None:
            print(f"Image not found for {json_file}")
            continue
        
        try:
            result, _, _ = load_and_draw_masks(img_path, json_file)
            output_path = output_dir / f"{img_stem}_masked.jpg"
            cv2.imwrite(str(output_path), result)
            print(f"Processed: {img_path} -> {output_path}")
        except Exception as e:
            print(f"Error processing {img_path}: {e}")

# 使用示例
if __name__ == "__main__":
    image_path = "test_files/2_20210615122806_color.jpg"
    json_path = "test_files/2_20210615122806_color_contours.json"
    
    # 方法1: 直接绘制到图像上
    result, original_with_contours, mask_overlay = load_and_draw_masks(image_path, json_path)
    
    cv2.imshow('Result', result)
    cv2.imshow('Contours Only', original_with_contours)
    cv2.imshow('Mask Overlay', mask_overlay)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    # # 方法2: 重建mask数组
    # img = cv2.imread(image_path)
    # masks = create_individual_masks(img.shape, json_path)
    # print(f"Reconstructed {len(masks)} masks with shape {masks.shape}")