import numpy as np
import pyrealsense2 as rs  # COMMENTED OUT - RealSense dependency for Orbbec migration
from scipy.ndimage import label, distance_transform_edt
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
import matplotlib
import cv2
import torch
import math
import random

import os
import time
from datetime import datetime
try:
    import pandas as pd
except ImportError:
    print("警告: pandas未安装，数据记录功能将不可用")
    pd = None

try:
    from sklearn.cluster import KMeans
except ImportError:
    print("警告: sklearn未安装，聚类功能将不可用")
    KMeans = None

try:
    import diffusers
except ImportError:
    print("警告: diffusers未安装，法线计算功能将不可用")
    diffusers = None

'''
第5版蘑菇采摘机器人视觉系统核心处理模块
支持多种策略的灵活组合：
- 采摘点位策略：几何中心、最高点、最缓点
- 尺寸判断策略：最长线段、星型线段、圆形拟合
- 顺序规划策略：按深度、凸包算法、圆度排序、占用图排序
- 聚类功能：位置聚类和可视化
- 法线计算：Marigold模型集成

参数控制格式: [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]
例如: [1, 2, 3, 1, 2] 表示几何中心+星型线段+圆度排序+开启聚类+不计算法线

【250803】第5版系统架构设计和实现
【250804-07】5th代码远程实机部署同步调试；大部分功能测试通过，但是1.聚类算法有bug；2.法线模型尚未部署测试
【250808】在5th_pre的基础上单独调试聚类功能。已知test_clustering_validation.py中的实现没有问题。部署在4号机上开展测试，但是聚类功能仍有bug，保持关闭状态
【250813】尝试对聚类功能进行debug。auto:将聚类和顺序规划结合起来，先聚类，然后从簇内执行顺序规划；在4号机上部署调试成功，上传飞书并下载本地，从dev_auto改名为dev
【250925】@TongXiang，本地开发，将基于占用图的采摘顺序和方向功能增加进来
'''

BIAS = 0
BIAS_MAXX = 7
BIAS_MAXY = 5
RSLUX = 848
RSLUY = 480
imgCenter = [RSLUX, RSLUY]
OFFSETX = -2
OFFSETY = -4

# ==================== 采摘点位策略模块 ====================

class PickingPointStrategy:
    """采摘点位策略基类"""

    def get_picking_point(self, mask, depth_frame, depth_intrin, normal_map=None):
        """
        获取采摘点位

        Args:
            mask: 2D布尔数组，表示mask区域
            depth_frame: 深度帧
            depth_intrin: 深度相机内参
            normal_map: 法线图（可选）

        Returns:
            tuple: (pixel_x, pixel_y, world_point, additional_info)
        """
        raise NotImplementedError

class GeometricCenterStrategy(PickingPointStrategy):
    """策略1: 几何中心点"""

    def get_picking_point(self, mask, depth_frame, depth_intrin, normal_map=None):
        # 计算mask的几何中心（质心）
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] == 0:
            return None, None, None, None

        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])

        # 获取深度信息
        pick_depth = depth_frame.get_distance(center_x, center_y)
        if pick_depth == 0:
            return None, None, None, None

        # 计算世界坐标
        world_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [center_x, center_y], pick_depth)  # COMMENTED OUT - RealSense
        # world_point = [0, 0, 0]  # Placeholder for Orbbec migration

        return center_x, center_y, world_point, {"type": "geometric_center"}

class HighestPointStrategy(PickingPointStrategy):
    """策略2: 最高点（深度值最小的点）"""

    def get_picking_point(self, mask, depth_frame, depth_intrin, normal_map=None):
        # 缩小mask边界以避免边缘噪声
        shrunk_mask = self._shrink_mask(mask, shrink_pixels=20)

        # 获取mask区域内的所有坐标
        mask_coords = np.argwhere(shrunk_mask)
        if mask_coords.size == 0:
            return None, None, None, None

        # 提取对应的深度值
        depths = np.array([depth_frame.get_distance(x, y) for y, x in mask_coords])

        # 过滤掉深度为0的点
        valid_indices = depths > 0
        if not np.any(valid_indices):
            return None, None, None, None

        valid_coords = mask_coords[valid_indices]
        valid_depths = depths[valid_indices]

        # 找到深度最小值的点（最高点）
        highest_idx = np.argmin(valid_depths)
        highest_coord = valid_coords[highest_idx]
        highest_depth = valid_depths[highest_idx]

        # 计算世界坐标
        world_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [highest_coord[1], highest_coord[0]], highest_depth)  # COMMENTED OUT - RealSense
        # world_point = [0, 0, 0]  # Placeholder for Orbbec migration

        return highest_coord[1], highest_coord[0], world_point, {"type": "highest_point", "depth": highest_depth}

    def _shrink_mask(self, mask, shrink_pixels=10):
        """缩小mask边界"""
        mask = mask.astype(bool)

        if shrink_pixels <= 0:
            return mask

        # 找到mask的边界
        rows = np.any(mask, axis=1)
        cols = np.any(mask, axis=0)

        if not np.any(rows) or not np.any(cols):
            return np.zeros_like(mask)

        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]

        # 计算新的边界
        new_rmin = rmin + shrink_pixels
        new_rmax = rmax - shrink_pixels
        new_cmin = cmin + shrink_pixels
        new_cmax = cmax - shrink_pixels

        # 边界检查
        if new_rmin >= new_rmax or new_cmin >= new_cmax:
            return np.zeros_like(mask)

        # 创建缩小后的矩形区域
        shrunk_mask = np.zeros_like(mask, dtype=bool)
        shrunk_mask[new_rmin:new_rmax+1, new_cmin:new_cmax+1] = True

        # 与原始mask交集
        return shrunk_mask & mask

class SafestPointStrategy(PickingPointStrategy):
    """策略3: 最缓点（基于法线变化率的安全点）"""

    def get_picking_point(self, mask, depth_frame, depth_intrin, normal_map=None):
        if normal_map is None:
            # 如果没有法线图，回退到几何中心策略！
            fallback = GeometricCenterStrategy()
            return fallback.get_picking_point(mask, depth_frame, depth_intrin, normal_map)

        # 计算法线变化率
        gradient_map = self._calculate_normal_gradient(normal_map, mask)

        # 找到法线变化最小的连通区域
        stable_region = self._find_stable_region(mask, gradient_map)

        if not np.any(stable_region):
            # 如果没有找到稳定区域，回退到几何中心策略！
            fallback = GeometricCenterStrategy()
            return fallback.get_picking_point(mask, depth_frame, depth_intrin, normal_map)

        # 计算稳定区域的几何中心作为采摘点！
        M = cv2.moments(stable_region.astype(np.uint8))
        if M["m00"] == 0:
            fallback = GeometricCenterStrategy()
            return fallback.get_picking_point(mask, depth_frame, depth_intrin, normal_map)

        safe_x = int(M["m10"] / M["m00"])
        safe_y = int(M["m01"] / M["m00"])

        # 获取深度信息
        pick_depth = depth_frame.get_distance(safe_x, safe_y)
        if pick_depth == 0:
            return None, None, None, None

        # 计算世界坐标
        world_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [safe_x, safe_y], pick_depth)

        # 获取法线向量
        normal_vector = normal_map[safe_y, safe_x] if normal_map is not None else [0, 0, 1]

        return safe_x, safe_y, world_point, {
            "type": "safest_point",
            "normal": normal_vector,
            "stable_region": stable_region
        }

    def _calculate_normal_gradient(self, normal_map, mask, neighborhood_radius=10):
        """计算mask区域内法线图的变化速率"""
        # 转换为numpy数组
        normal_np = normal_map.numpy() if isinstance(normal_map, torch.Tensor) else normal_map

        # 确保mask是布尔类型
        mask = mask.astype(bool)

        # 创建全零的变化速率图
        H, W, _ = normal_np.shape
        gradient_magnitude = np.zeros((H, W), dtype=float)

        # 如果mask为空，直接返回全零图
        if not np.any(mask):
            return gradient_magnitude

        # 归一化法线向量
        normals_unit = normal_np / (np.linalg.norm(normal_np, axis=2, keepdims=True) + 1e-8)

        # 获取mask内的坐标
        y_coords, x_coords = np.where(mask)

        # 对mask内的每个点计算局部邻域内的变化率
        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
            # 当前点的法线向量
            current_normal = normals_unit[y, x]

            # 定义邻域范围
            y_min = max(0, y - neighborhood_radius)
            y_max = min(H - 1, y + neighborhood_radius)
            x_min = max(0, x - neighborhood_radius)
            x_max = min(W - 1, x + neighborhood_radius)

            # 提取局部窗口
            local_window = mask[y_min:y_max+1, x_min:x_max+1]
            local_normals = normals_unit[y_min:y_max+1, x_min:x_max+1]

            # 计算局部窗口内所有mask内点与当前点的余弦距离
            current_y_local, current_x_local = y - y_min, x - x_min

            # 创建一个同样大小的mask来排除当前点
            exclude_self = np.ones_like(local_window, dtype=bool)
            if 0 <= current_y_local < exclude_self.shape[0] and 0 <= current_x_local < exclude_self.shape[1]:
                exclude_self[current_y_local, current_x_local] = False

            # 只考虑局部窗口内且在mask内且不是自身的点
            valid_points = local_window & exclude_self

            if np.any(valid_points):
                # 展平有效点的坐标和对应的法线向量
                flat_indices = np.where(valid_points.flatten())[0]
                valid_normals = local_normals.reshape(-1, 3)[flat_indices]

                # 计算余弦相似度
                cosine_sims = np.dot(valid_normals, current_normal)
                cosine_sims = np.clip(cosine_sims, -1.0, 1.0)

                # 计算余弦距离
                distances = 1 - np.abs(cosine_sims)

                # 计算平均变化率
                gradient_magnitude[y, x] = np.mean(distances)

        # 仅在mask区域内进行归一化
        mask_values = gradient_magnitude[mask]
        if len(mask_values) > 0:
            mask_min = np.min(mask_values)
            mask_max = np.max(mask_values)

            if mask_max > mask_min:
                # 归一化到[0, 1]范围
                normalized_values = (mask_values - mask_min) / (mask_max - mask_min)
                gradient_magnitude[mask] = normalized_values

        return gradient_magnitude

    def _find_stable_region(self, mask, gradient, target_ratio=0.2):
        """找到法线变化最小的连通区域（占mask面积的20%）"""
        # 仅考虑mask内部的梯度
        masked_gradient = gradient.copy()
        masked_gradient[mask == 0] = float('inf')

        # 计算目标面积
        mask_area = np.sum(mask)
        target_area = int(mask_area * target_ratio)

        # 基于梯度值排序
        flat_indices = np.argsort(masked_gradient.flatten())
        flat_mask = mask.flatten()
        valid_indices = [idx for idx in flat_indices if flat_mask[idx] > 0]

        # 选择最低梯度的点，直到达到目标面积
        stability_mask = np.zeros_like(mask, dtype=bool)
        h, w = mask.shape
        count = 0

        for idx in valid_indices:
            if count >= target_area:
                break
            y, x = idx // w, idx % w
            stability_mask[y, x] = True
            count += 1

        # 进行连通区域分析
        labeled_array, num_features = label(stability_mask)

        # 选择最大的连通区域
        if num_features > 0:
            sizes = np.bincount(labeled_array.flatten())[1:]  # 跳过背景
            largest_label = np.argmax(sizes) + 1  # +1 因为背景是0
            stable_region = (labeled_array == largest_label)
        else:
            stable_region = np.zeros_like(mask, dtype=bool)

        return stable_region

# 采摘点位策略工厂
def get_picking_point_strategy(strategy_id):
    """根据策略ID获取对应的采摘点位策略"""
    strategies = {
        1: GeometricCenterStrategy(),
        2: HighestPointStrategy(),
        3: SafestPointStrategy()
    }
    return strategies.get(strategy_id, GeometricCenterStrategy())

# ==================== 尺寸判断策略模块 ====================

class SizeEstimationStrategy:
    """尺寸判断策略基类"""

    def estimate_size(self, mask, depth_frame, depth_intrin, SIZEBIAS=0):
        """
        估算目标尺寸

        Args:
            mask: 2D布尔数组，表示mask区域
            depth_frame: 深度帧
            depth_intrin: 深度相机内参
            SIZEBIAS: 尺寸偏差

        Returns:
            tuple: (size, measurement_points, visualization_info)
        """
        raise NotImplementedError

class LongestDiameterStrategy(SizeEstimationStrategy):
    """策略1: 过几何中心最长线段的三分点"""

    def estimate_size(self, mask, depth_frame, depth_intrin, SIZEBIAS=0):
        # 找到经过几何中心的最长直径
        diameter_info = self._find_mask_diameter(mask)
        if diameter_info is None:
            return None, None, None

        start_point, end_point, center, third_point1, third_point2, contours = diameter_info

        # 获取三分点的深度信息
        rf1_depth = depth_frame.get_distance(int(third_point1[0]), int(third_point1[1]))
        rf2_depth = depth_frame.get_distance(int(third_point2[0]), int(third_point2[1]))

        if rf1_depth == 0 or rf2_depth == 0:
            return None, None, None

        # 计算三分点的世界坐标
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point1[0]), int(third_point1[1])], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point2[0]), int(third_point2[1])], rf2_depth)

        # 计算真实距离
        size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) * 3 + SIZEBIAS

        measurement_points = [third_point1, third_point2]
        visualization_info = {
            "type": "longest_diameter",
            "diameter_line": (start_point, end_point),
            "measurement_points": measurement_points,
            "center": center,
            "contours": contours
        }

        return size, measurement_points, visualization_info

    def _find_mask_diameter(self, mask, sample_ratio=0.5):
        """寻找经过mask几何中心且长度最大的直径"""
        # 确保mask是布尔类型
        mask = mask.astype(np.uint8)

        # 计算mask的几何中心（质心）
        M = cv2.moments(mask)
        if M["m00"] == 0:
            return None
        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        center = (center_x, center_y)

        # 找到mask的轮廓
        contours, _ = self._find_mask_contours(mask)
        if not contours:
            return None
        contours = [max(contours, key=cv2.contourArea)]

        # 合并所有轮廓点并按固定间隔采样
        all_contour_points = []
        for contour in contours:
            step = max(1, int(1 / sample_ratio))
            sampled_points = [point[0] for point in contour[::step]]
            all_contour_points.extend(sampled_points)

        if len(all_contour_points) < 2:
            return None

        # 寻找经过或接近中心的最长线段
        max_distance = 0
        best_pair = None
        threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)

        n = len(all_contour_points)
        for i in range(n):
            for j in range(i+1, n):
                p1 = all_contour_points[i]
                p2 = all_contour_points[j]

                # 计算点到线的距离
                if p1[0] == p2[0]:  # 垂直线
                    distance_to_center = abs(center_x - p1[0])
                elif p1[1] == p2[1]:  # 水平线
                    distance_to_center = abs(center_y - p1[1])
                else:
                    # 一般情况
                    A = (p2[1] - p1[1])
                    B = (p1[0] - p2[0])
                    C = (p2[0]*p1[1] - p1[0]*p2[1])
                    distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)

                # 如果线段经过或接近中心，计算长度
                if distance_to_center <= threshold:
                    distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
                    if distance > max_distance:
                        max_distance = distance
                        best_pair = (p1, p2)

        if best_pair is None:
            return None

        p1, p2 = best_pair

        # 计算三等分点
        third_point1 = (
            int(p1[0] + (p2[0] - p1[0]) / 3),
            int(p1[1] + (p2[1] - p1[1]) / 3)
        )
        third_point2 = (
            int(p1[0] + 2 * (p2[0] - p1[0]) / 3),
            int(p1[1] + 2 * (p2[1] - p1[1]) / 3)
        )

        return best_pair[0], best_pair[1], center, third_point1, third_point2, contours

    def _find_mask_contours(self, mask):
        """找到mask的轮廓"""
        mask_uint8 = mask.astype(np.uint8) * 255
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return [], (0, 0)

        # 找到最大的轮廓
        max_contour = max(contours, key=cv2.contourArea)

        # 计算最大轮廓的几何中心
        M = cv2.moments(max_contour)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            center = (cx, cy)
        else:
            center = (0, 0)

        return contours, center

class StarShapedStrategy(SizeEstimationStrategy):
    """策略2: 星型线段的三分点平均"""

    def estimate_size(self, mask, depth_frame, depth_intrin, SIZEBIAS=0):
        # 计算mask的几何中心
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] == 0:
            return None, None, None

        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        center = (center_x, center_y)

        # 找到mask的轮廓
        contours, _ = self._find_mask_contours(mask)
        if not contours:
            return None, None, None

        # 定义4个方向：0°, 45°, 90°, 135°
        directions = [0, 45, 90, 135]  # 度数
        all_sizes = []
        all_measurement_points = []
        all_lines = []

        for angle in directions:
            # 计算方向向量
            rad = np.radians(angle)
            dx = np.cos(rad)
            dy = np.sin(rad)

            # 找到该方向上与mask边界的交点
            intersections = self._find_line_mask_intersections(mask, center, (dx, dy))

            if len(intersections) >= 2:
                # 选择距离最远的两个交点
                distances = [np.sqrt((p[0] - center_x)**2 + (p[1] - center_y)**2) for p in intersections]
                sorted_indices = np.argsort(distances)
                p1 = intersections[sorted_indices[-1]]
                p2 = intersections[sorted_indices[-2]] if len(intersections) > 1 else intersections[0]

                # 计算三分点
                third_point1 = (
                    int(p1[0] + (p2[0] - p1[0]) / 3),
                    int(p1[1] + (p2[1] - p1[1]) / 3)
                )
                third_point2 = (
                    int(p1[0] + 2 * (p2[0] - p1[0]) / 3),
                    int(p1[1] + 2 * (p2[1] - p1[1]) / 3)
                )

                # 获取深度信息
                rf1_depth = depth_frame.get_distance(int(third_point1[0]), int(third_point1[1]))
                rf2_depth = depth_frame.get_distance(int(third_point2[0]), int(third_point2[1]))

                if rf1_depth > 0 and rf2_depth > 0:
                    # 计算世界坐标
                    rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point1[0]), int(third_point1[1])], rf1_depth)
                    rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point2[0]), int(third_point2[1])], rf2_depth)

                    # 计算距离
                    size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) * 3
                    all_sizes.append(size)
                    all_measurement_points.extend([third_point1, third_point2])
                    all_lines.append((p1, p2))

        if not all_sizes:
            return None, None, None

        # 计算平均尺寸
        avg_size = np.mean(all_sizes) + SIZEBIAS

        visualization_info = {
            "type": "star_shaped",
            "lines": all_lines,
            "measurement_points": all_measurement_points,
            "center": center,
            "contours": contours
        }

        return avg_size, all_measurement_points, visualization_info

    def _find_line_mask_intersections(self, mask, center, direction):
        """找到从中心点沿指定方向与mask边界的交点"""
        center_x, center_y = center
        dx, dy = direction

        intersections = []

        # 向两个方向延伸寻找交点
        for sign in [1, -1]:
            for t in range(1, max(mask.shape)):
                x = int(center_x + sign * t * dx)
                y = int(center_y + sign * t * dy)

                # 检查是否超出图像边界
                if x < 0 or x >= mask.shape[1] or y < 0 or y >= mask.shape[0]:
                    break

                # 检查是否到达mask边界
                if not mask[y, x]:
                    # 找到边界，记录前一个点作为交点
                    prev_x = int(center_x + sign * (t-1) * dx)
                    prev_y = int(center_y + sign * (t-1) * dy)
                    if 0 <= prev_x < mask.shape[1] and 0 <= prev_y < mask.shape[0] and mask[prev_y, prev_x]:
                        intersections.append((prev_x, prev_y))
                    break

        return intersections

    def _find_mask_contours(self, mask):
        """找到mask的轮廓"""
        mask_uint8 = mask.astype(np.uint8) * 255
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return [], (0, 0)

        max_contour = max(contours, key=cv2.contourArea)
        M = cv2.moments(max_contour)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            center = (cx, cy)
        else:
            center = (0, 0)

        return contours, center

class CircleFittingStrategy(SizeEstimationStrategy):
    """策略3: 圆形拟合的直径三分点"""

    def estimate_size(self, mask, depth_frame, depth_intrin, SIZEBIAS=0):
        # 计算mask的几何中心和面积
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] == 0:
            return None, None, None

        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        center = (center_x, center_y)

        # 计算mask面积
        mask_area = np.sum(mask)

        # 根据面积计算等效圆的半径
        equivalent_radius = np.sqrt(mask_area / np.pi)

        # 找到mask的轮廓
        contours, _ = self._find_mask_contours(mask)
        if not contours:
            return None, None, None

        # 在垂直方向上找到圆形直径的端点
        # 使用等效半径在垂直方向上定义直径
        p1 = (center_x, int(center_y - equivalent_radius))
        p2 = (center_x, int(center_y + equivalent_radius))

        # 确保点在mask内部，如果不在则调整到mask边界
        p1 = self._adjust_point_to_mask(p1, mask, center)
        p2 = self._adjust_point_to_mask(p2, mask, center)

        # 计算三分点
        third_point1 = (
            int(p1[0] + (p2[0] - p1[0]) / 3),
            int(p1[1] + (p2[1] - p1[1]) / 3)
        )
        third_point2 = (
            int(p1[0] + 2 * (p2[0] - p1[0]) / 3),
            int(p1[1] + 2 * (p2[1] - p1[1]) / 3)
        )

        # 获取深度信息
        rf1_depth = depth_frame.get_distance(int(third_point1[0]), int(third_point1[1]))
        rf2_depth = depth_frame.get_distance(int(third_point2[0]), int(third_point2[1]))

        if rf1_depth == 0 or rf2_depth == 0:
            return None, None, None

        # 计算世界坐标
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point1[0]), int(third_point1[1])], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(third_point2[0]), int(third_point2[1])], rf2_depth)

        # 计算真实距离
        size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) * 3 + SIZEBIAS

        measurement_points = [third_point1, third_point2]
        visualization_info = {
            "type": "circle_fitting",
            "diameter_line": (p1, p2),
            "measurement_points": measurement_points,
            "center": center,
            "radius": equivalent_radius,
            "contours": contours
        }

        return size, measurement_points, visualization_info

    def _adjust_point_to_mask(self, point, mask, center):
        """将点调整到mask边界内"""
        x, y = point
        center_x, center_y = center

        # 如果点已经在mask内，直接返回
        if 0 <= x < mask.shape[1] and 0 <= y < mask.shape[0] and mask[y, x]:
            return point

        # 计算从中心到目标点的方向
        dx = x - center_x
        dy = y - center_y

        # 沿着这个方向寻找mask边界
        for t in np.linspace(0.1, 1.0, 20):
            test_x = int(center_x + t * dx)
            test_y = int(center_y + t * dy)

            if 0 <= test_x < mask.shape[1] and 0 <= test_y < mask.shape[0]:
                if mask[test_y, test_x]:
                    continue
                else:
                    # 找到边界，返回前一个有效点
                    prev_t = max(0.1, t - 0.05)
                    final_x = int(center_x + prev_t * dx)
                    final_y = int(center_y + prev_t * dy)
                    return (final_x, final_y)

        # 如果没找到合适的点，返回中心点
        return center

    def _find_mask_contours(self, mask):
        """找到mask的轮廓"""
        mask_uint8 = mask.astype(np.uint8) * 255
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return [], (0, 0)

        max_contour = max(contours, key=cv2.contourArea)
        M = cv2.moments(max_contour)
        if M["m00"] > 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            center = (cx, cy)
        else:
            center = (0, 0)

        return contours, center

# 尺寸判断策略工厂
def get_size_estimation_strategy(strategy_id):
    """根据策略ID获取对应的尺寸判断策略"""
    strategies = {
        1: LongestDiameterStrategy(),
        2: StarShapedStrategy(),
        3: CircleFittingStrategy()
    }
    return strategies.get(strategy_id, LongestDiameterStrategy())

# ==================== 采摘顺序规划策略模块 ====================

class SequencePlanningStrategy:
    """采摘顺序规划策略基类"""

    def plan_sequence(self, targets_info):
        """
        规划采摘顺序

        Args:
            targets_info: 目标信息列表，每个元素包含位置、深度、mask等信息

        Returns:
            list: 排序后的目标索引列表
        """
        raise NotImplementedError

class DepthBasedStrategy(SequencePlanningStrategy):
    """策略1: 按深度从低到高排序"""

    def plan_sequence(self, targets_info):
        if not targets_info:
            return []

        # 提取深度信息并排序
        depth_indices = []
        for i, target in enumerate(targets_info):
            if 'world_point' in target and target['world_point'] is not None:
                depth = target['world_point'][2]  # Z坐标表示深度
                depth_indices.append((i, depth))

        # 按深度从小到大排序（深度小表示距离近，优先采摘）
        depth_indices.sort(key=lambda x: x[1])

        return [idx for idx, _ in depth_indices]

class ConvexHullStrategy(SequencePlanningStrategy):
    """策略2: 基于凸包从外到内排序"""

    def plan_sequence(self, targets_info):
        if not targets_info:
            return []

        # 提取像素坐标
        points = []
        valid_indices = []
        for i, target in enumerate(targets_info):
            if 'pixel_x' in target and 'pixel_y' in target:
                points.append([target['pixel_x'], target['pixel_y']])
                valid_indices.append(i)

        if len(points) < 3:
            # 如果点数少于3个，直接按距离图像中心的距离排序
            return self._sort_by_distance_to_center(targets_info)

        points = np.array(points)

        # 使用Graham扫描算法计算凸包
        hull_indices = self._graham_scan(points)

        if not hull_indices:
            return self._sort_by_distance_to_center(targets_info)

        # 将凸包上的点放在前面，内部点放在后面
        hull_set = set(hull_indices)
        outer_points = [valid_indices[i] for i in hull_indices]
        inner_points = [valid_indices[i] for i in range(len(points)) if i not in hull_set]

        # 对内部点按距离凸包中心的距离排序
        if inner_points and hull_indices:
            hull_center = np.mean(points[hull_indices], axis=0)
            inner_distances = []
            for idx in inner_points:
                original_idx = valid_indices.index(idx)
                point = points[original_idx]
                dist = np.linalg.norm(point - hull_center)
                inner_distances.append((idx, dist))

            inner_distances.sort(key=lambda x: x[1])
            inner_points = [idx for idx, _ in inner_distances]

        return outer_points + inner_points

    def _graham_scan(self, points):
        """Graham扫描算法计算凸包"""
        def cross_product(o, a, b):
            return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])

        n = len(points)
        if n < 3:
            return list(range(n))

        # 找到最下面的点（y坐标最小，如果相同则x坐标最小）
        start_idx = 0
        for i in range(1, n):
            if (points[i][1] < points[start_idx][1] or
                (points[i][1] == points[start_idx][1] and points[i][0] < points[start_idx][0])):
                start_idx = i

        # 按极角排序
        def polar_angle(p):
            dx = p[0] - points[start_idx][0]
            dy = p[1] - points[start_idx][1]
            return np.arctan2(dy, dx)

        indices = list(range(n))
        indices.remove(start_idx)
        indices.sort(key=lambda i: (polar_angle(points[i]),
                                   np.linalg.norm(points[i] - points[start_idx])))

        # Graham扫描
        hull = [start_idx]
        for i in indices:
            while (len(hull) > 1 and
                   cross_product(points[hull[-2]], points[hull[-1]], points[i]) <= 0):
                hull.pop()
            hull.append(i)

        return hull

    def _sort_by_distance_to_center(self, targets_info):
        """按距离图像中心的距离排序"""
        if not targets_info:
            return []

        center_x, center_y = imgCenter[0] // 2, imgCenter[1] // 2
        distances = []

        for i, target in enumerate(targets_info):
            if 'pixel_x' in target and 'pixel_y' in target:
                dist = np.sqrt((target['pixel_x'] - center_x)**2 + (target['pixel_y'] - center_y)**2)
                distances.append((i, dist))

        distances.sort(key=lambda x: x[1], reverse=True)  # 从外到内
        return [idx for idx, _ in distances]

class CircularityBasedStrategy(SequencePlanningStrategy):
    """策略3: 按圆度从圆到不圆排序"""

    def plan_sequence(self, targets_info):
        if not targets_info:
            return []

        # 计算每个目标的圆度
        circularities = []
        for i, target in enumerate(targets_info):
            if 'mask' in target:
                circularity = self._calculate_mask_circularity(target['mask'])
                circularities.append((i, circularity))

        # 按圆度从高到低排序（圆度高的先采摘）
        circularities.sort(key=lambda x: x[1], reverse=True)

        return [idx for idx, _ in circularities]

    def _calculate_mask_circularity(self, mask):
        """计算mask的圆度"""
        # 确保mask是uint8类型
        mask_uint8 = mask.astype(np.uint8) * 255

        # 找到轮廓
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return 0.0

        # 选择最大的轮廓
        max_contour = max(contours, key=cv2.contourArea)

        # 计算面积和周长
        area = cv2.contourArea(max_contour)
        perimeter = cv2.arcLength(max_contour, True)

        if perimeter == 0:
            return 0.0

        # 圆度公式: 4π * area / perimeter^2
        # 完美圆形的圆度为1，其他形状的圆度小于1
        circularity = 4 * np.pi * area / (perimeter * perimeter)

        return circularity

class OccupancyMapStrategy(SequencePlanningStrategy):
    """策略4: 基于占用图的最优自由点分配排序"""

    def plan_sequence(self, targets_info):
        if not targets_info:
            return []

        # 获取图像尺寸（从第一个目标的mask信息推断）
        if not targets_info[0].get('mask_info'):
            # 如果没有mask信息，回退到圆度排序
            return CircularityBasedStrategy().plan_sequence(targets_info)

        # 从targets_info中提取mask信息
        masks_data = []
        for i, target in enumerate(targets_info):
            mask_info = target.get('mask_info', {})
            if 'mask' in mask_info:
                masks_data.append({
                    'index': i,
                    'mask': mask_info['mask'],
                    'target_info': target
                })

        if not masks_data:
            return CircularityBasedStrategy().plan_sequence(targets_info)

        # 获取图像尺寸
        img_shape = masks_data[0]['mask'].shape

        # 执行占用图算法
        try:
            sequence_indices, optimal_points = self._execute_occupancy_algorithm(masks_data, img_shape)

            # 将最优点信息添加到targets_info中
            for seq_idx, (target_idx, optimal_point) in enumerate(zip(sequence_indices, optimal_points)):
                if 0 <= target_idx < len(targets_info):
                    targets_info[target_idx]['optimal_point_pixel'] = optimal_point
                    targets_info[target_idx]['sequence_order'] = seq_idx + 1

            return sequence_indices

        except Exception as e:
            print(f"占用图算法执行失败，回退到圆度排序: {e}")
            return CircularityBasedStrategy().plan_sequence(targets_info)

    def _execute_occupancy_algorithm(self, masks_data, img_shape):
        """执行占用图算法的核心逻辑"""
        # 初始化占用图
        occupancy_map = np.zeros(img_shape[:2], dtype=np.int32)

        # 准备mask信息
        mask_info_list = []
        for mask_data in masks_data:
            mask = mask_data['mask']

            # 计算几何中心
            center = self._calculate_geometric_center_from_mask(mask)
            if center is None:
                continue

            # 创建扩展mask
            extended_mask = self._create_extended_mask(mask, center)

            mask_info_list.append({
                'index': mask_data['index'],
                'center': center,
                'original_mask': mask,
                'extended_mask': extended_mask
            })

        # 构建初始占用图
        for mask_info in mask_info_list:
            # 原始mask区域 +100
            occupancy_map[mask_info['original_mask'] == 255] += 100

            # 扩展区域（环形区域）+1
            annular_region = mask_info['extended_mask'] - mask_info['original_mask']
            occupancy_map[annular_region == 255] += 1

        # 迭代处理所有mask
        sequence_indices = []
        optimal_points = []
        processed_masks = set()

        while len(processed_masks) < len(mask_info_list):
            # 选择几何中心占用值最小的mask
            min_center_occupancy = float('inf')
            selected_mask_idx = None

            for i, mask_info in enumerate(mask_info_list):
                if i in processed_masks:
                    continue

                center_x, center_y = mask_info['center']
                center_occupancy = occupancy_map[center_y, center_x]

                if center_occupancy < min_center_occupancy:
                    min_center_occupancy = center_occupancy
                    selected_mask_idx = i

            if selected_mask_idx is None:
                break

            selected_mask = mask_info_list[selected_mask_idx]

            # 在扩展区域内找到占用值最小的点
            optimal_point = self._find_min_occupancy_point_in_region(
                occupancy_map, selected_mask['extended_mask'], selected_mask['center']
            )

            # 记录结果
            sequence_indices.append(selected_mask['index'])
            optimal_points.append(optimal_point)

            # 更新占用图 - 减去当前mask的贡献
            occupancy_map[selected_mask['original_mask'] == 255] -= 100
            annular_region = selected_mask['extended_mask'] - selected_mask['original_mask']
            occupancy_map[annular_region == 255] -= 1

            processed_masks.add(selected_mask_idx)

        return sequence_indices, optimal_points

    def _calculate_geometric_center_from_mask(self, mask):
        """从mask计算几何中心"""
        M = cv2.moments(mask.astype(np.uint8))
        if M["m00"] == 0:
            return None

        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        return (center_x, center_y)

    def _create_extended_mask(self, mask, center):
        """创建扩展mask"""
        # 获取mask的轮廓
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return mask.copy()

        # 使用最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        contour_points = largest_contour.reshape(-1, 2)

        # 扩展轮廓点
        extended_points = self._extend_contour_from_center(contour_points, center)

        # 创建扩展mask
        extended_mask = np.zeros_like(mask, dtype=np.uint8)
        if extended_points:
            extended_contour = np.array(extended_points, dtype=np.int32).reshape(-1, 1, 2)
            cv2.fillPoly(extended_mask, [extended_contour], 255)

        return extended_mask

    def _extend_contour_from_center(self, contour_points, center):
        """从几何中心向外扩展轮廓点，距离加倍"""
        extended_points = []
        center_x, center_y = center

        for point in contour_points:
            x, y = point
            # 计算从中心到当前点的向量
            dx = x - center_x
            dy = y - center_y

            # 扩展距离（加倍）
            extended_x = center_x + 2 * dx
            extended_y = center_y + 2 * dy

            extended_points.append([int(extended_x), int(extended_y)])

        return extended_points

    def _find_min_occupancy_point_in_region(self, occupancy_map, extended_mask, original_center):
        """在扩展区域内找到占用值最小的点"""
        # 获取扩展区域内的所有点
        extended_coords = np.where(extended_mask == 255)

        if len(extended_coords[0]) == 0:
            return original_center

        min_occupancy = float('inf')
        best_point = original_center

        # 遍历扩展区域内的所有点
        for i in range(len(extended_coords[0])):
            y, x = extended_coords[0][i], extended_coords[1][i]
            occupancy_value = occupancy_map[y, x]

            if occupancy_value < min_occupancy:
                min_occupancy = occupancy_value
                best_point = (x, y)

        return best_point

# 采摘顺序规划策略工厂
def get_sequence_planning_strategy(strategy_id):
    """根据策略ID获取对应的采摘顺序规划策略"""
    strategies = {
        1: DepthBasedStrategy(),
        2: ConvexHullStrategy(),
        3: CircularityBasedStrategy(),
        4: OccupancyMapStrategy()
    }
    return strategies.get(strategy_id, DepthBasedStrategy())

# ==================== 聚类功能模块 ====================

class PositionClustering:
    """基于位置的目标聚类"""

    def __init__(self, n_clusters=None, max_distance=100, clustering_method=1):
        """
        初始化聚类器

        Args:
            n_clusters: 聚类数量（仅K-means使用；None表示自动）
            max_distance: DBSCAN的eps（像素）
            clustering_method: 1=DBSCAN, 2=K-means, 3=关闭聚类
        """
        self.n_clusters = n_clusters
        self.max_distance = max_distance  # DBSCAN的eps
        self.clustering_method = clustering_method

    def cluster_targets(self, targets_info):
        """
        对目标进行聚类（支持三种模式：1=DBSCAN，2=K-means，3=关闭聚类）

        - DBSCAN：采用test_clustering_validation.py验证方案 + 噪声点后处理
        - K-means：采用test_clustering_validation.py中simple_kmeans的思路（不依赖sklearn）
        - 关闭聚类：所有目标都划分为一簇

        Returns保持一致，便于可视化与后续处理。
        """
        if not targets_info:
            return {
                'cluster_labels': [],
                # 'cluster_centers': [],
                'cluster_colors': [],
                # 'cluster_sizes': [],
                'sorted_clusters': []
            }

        # 提取位置信息（像素坐标）
        coordinates = []  # [(x, y), ...]
        valid_indices = []
        for i, target in enumerate(targets_info):
            if 'pixel_x' in target and 'pixel_y' in target:
                try:
                    x = float(target['pixel_x'])
                    y = float(target['pixel_y'])
                    coordinates.append((x, y))
                    valid_indices.append(i)  # 与原始的target_info保持一致
                except Exception:
                    continue

        # 处理“关闭聚类”或者目标小于2个：所有目标划分为一簇
        if self.clustering_method == 3 or len(coordinates) <= 1:
            print("==========关闭聚类或者蘑菇数量小于2，所有目标划分为一簇")
            labels = [0] * len(coordinates)  # 所有目标的label都是0
            # centers = [list(coords) for coords in coordinates]
            colors = [(255, 0, 0)] * (len(set(labels)) if labels else 0)  # 全是红色
            # sizes = [1] * len(coordinates)
            sorted_clusters = [0] * len(set(labels))  # set的作用是为了去重
            return {
                'cluster_labels': labels,
                # 'cluster_centers': centers,
                'cluster_colors': colors,
                # 'cluster_sizes': sizes,
                'sorted_clusters': sorted_clusters,
                'valid_indices': valid_indices
            }

        # 选择算法
        if self.clustering_method == 2:
            # K-means（不依赖sklearn的简化实现）
            print("==========使用K-means聚类（simple_kmeans）")
            k = self.n_clusters
            if not k or k <= 0:  # 如果不传n_clusters参数，或者传入的值小于等于0，则自动确定聚类数量
                # auto k: mimic prior logic min(5, max(3, N//12)) but cap to sensible range
                k = min(8, max(2, len(coordinates) // 12 or 2))
            k_labels, k_centers = self._simple_kmeans(coordinates, k=k)
            # 规范化标签到0..K-1
            unique = sorted(list(set(k_labels)))  # 对聚簇的结果label进行排序,unique中的元素是去重后的
            label_map = {old: new for new, old in enumerate(unique)}  # 构建一个映射表，将unique中的元素映射为0, 1, 2...
            remapped_labels = [label_map[l] for l in k_labels]  # 映射后的label
            n_clusters = len(unique)  # 聚类数量
            # cluster_centers = [[float(c[0]), float(c[1])] for c in k_centers]
            cluster_sizes = [remapped_labels.count(cid) for cid in range(n_clusters)]  # 簇的大小，即该簇包含了多少个目标
        else:
            # DBSCAN
            eps = int(self.max_distance) if self.max_distance else 70  # 如果不传max_distance参数，则使用默认值70
            min_samples = 2
            print(f"使用DBSCAN聚类，eps={eps}, min_samples={min_samples}")
            raw_labels = self._simple_dbscan(coordinates, eps=eps, min_samples=min_samples)
            processed_labels = self._assign_noise_to_nearest_cluster(coordinates, raw_labels)  # 将噪声点分配给最近的簇
            unique = sorted([l for l in set(processed_labels) if l >= 0])  # 去重
            if not unique:
                processed_labels = [0 for _ in processed_labels]
                unique = [0]
            label_map = {old: new for new, old in enumerate(unique)}  # 构建一个映射表，将unique中的元素映射为0, 1, 2...
            remapped_labels = [label_map[l] if l in label_map else -1 for l in processed_labels]  # 映射后的label
            n_clusters = len(unique)
            # cluster_centers = []
            cluster_sizes = [0] * n_clusters
            for cid in range(n_clusters):
                pts = [coordinates[i] for i, lab in enumerate(remapped_labels) if lab == cid]
                if pts:
                    # cx = sum(p[0] for p in pts) / len(pts)
                    # cy = sum(p[1] for p in pts) / len(pts)
                    # cluster_centers.append([cx, cy])
                    cluster_sizes[cid] = len(pts)
                else:
                    # cluster_centers.append([0.0, 0.0])
                    cluster_sizes[cid] = 0

        # 生成聚类颜色（HSV色彩空间均匀分布）
        cluster_colors = self._generate_cluster_colors(n_clusters)  # 有多少簇，就有多少个颜色
        # 单簇时强制使用纯红色，满足“单簇全红”可视化要求
        if n_clusters == 1:
            cluster_colors = [(255, 0, 0)]

        # 按簇大小排序（大簇优先）
        cluster_size_pairs = list(zip(range(n_clusters), cluster_sizes))  # 构建一个列表，每个元素是一个元组，第一个元素是簇的索引，第二个元素是簇的大小
        cluster_size_pairs.sort(key=lambda x: x[1], reverse=True)  # 按簇大小降序排列
        sorted_clusters = [label for label, _ in cluster_size_pairs]  # 提取排序后的簇索引
        for cid, size in cluster_size_pairs:
            print(f"==========簇{cid}: {size}个目标")
        print(f"簇排序结果（按大小）: {sorted_clusters}")

        return {
            'cluster_labels': remapped_labels,
            # 'cluster_centers': cluster_centers,
            'cluster_colors': cluster_colors,
            # 'cluster_sizes': cluster_sizes,
            'sorted_clusters': sorted_clusters,
            'valid_indices': valid_indices
        }

    def _simple_dbscan(self, points, eps=70, min_samples=2):
        """
        简单的DBSCAN聚类实现（参考test_clustering_validation.py）
        Args:
            points: 坐标点列表 [(x, y), ...]
            eps: 邻域半径（像素）
            min_samples: 最小样本数
        Returns:
            list: cluster_labels (-1表示噪声点)
        """
        n_points = len(points)
        labels = [-2] * n_points  # -2未处理, -1噪声, >=0簇标签
        cluster_id = 0

        def dist(p, q):
            """
            计算两点之间的距离
            """
            return math.sqrt((p[0]-q[0])**2 + (p[1]-q[1])**2)

        def get_neighbors(idx):
            """
            获取指定点的邻居
            """
            neighbors = []
            for j in range(n_points):
                if j == idx:  # 跳过自身
                    continue
                if dist(points[idx], points[j]) <= eps:  # 如果两点距离小于eps，则将j加入邻居列表
                    neighbors.append(j)
            return neighbors

        for i in range(n_points):
            if labels[i] != -2:  # 已经过处理，跳过
                continue
            neighbors = get_neighbors(i)
            if len(neighbors) < min_samples:  # 如果邻居数量小于min_samples，则为噪声
                labels[i] = -1  # 噪声
            else:
                labels[i] = cluster_id  # 簇标签从0开始
                seed = neighbors[:]  # 将当前点的邻居加入种子列表
                j = 0
                while j < len(seed):
                    nb = seed[j]  # 取出种子列表中的下一个点
                    if labels[nb] == -1:
                        labels[nb] = cluster_id  # 邻居中的噪声变为边界点
                    elif labels[nb] == -2:
                        labels[nb] = cluster_id  # 邻居中的未处理变为边界点
                        nb_neighbors = get_neighbors(nb)  # 获取当前种子的邻居
                        if len(nb_neighbors) >= min_samples:  # 如果邻居数量大于等于min_samples，则将邻居的邻居加入种子列表
                            for nbi in nb_neighbors:
                                if nbi not in seed:
                                    seed.append(nbi)
                    j += 1
                cluster_id += 1
        print('dbscan noise points num: ', labels.count(-1))
        return labels

    def _simple_kmeans(self, points, k=3, max_iterations=50):
        """
        简单的K-means聚类实现（不依赖sklearn），来源于test_clustering_validation.py思路
        Args:
            points: [(x, y), ...]
            k: 聚类数量
            max_iterations: 最大迭代次数
        Returns:
            (labels, centers)
        """
        if len(points) <= k:  # 如果目标点的数量小于等于聚类数量，则令所有点划分为一簇
            return [0] * len(points), points
        # 随机初始化聚类中心（固定随机种子，保持可重复性）
        random.seed(42)
        centers = random.sample(points, k)  # 随机选择k个点作为初始中心
        for _ in range(max_iterations):  # 迭代的作用是为了让中心点尽量靠近簇的中心
            # 分配簇的标签
            labels = []
            for p in points:
                dists = [math.sqrt((p[0]-c[0])**2 + (p[1]-c[1])**2) for c in centers]  # 计算该点到所有中心点的距离
                labels.append(dists.index(min(dists)))  # 找到距离最近的中心点的索引，作为该点的簇标签
            # 更新中心，其目的是使中心点尽量靠近簇的中心
            new_centers = []
            for cid in range(k):
                pts = [points[i] for i, lab in enumerate(labels) if lab == cid]  # 找到属于该簇的所有点
                if pts:
                    cx = sum(p[0] for p in pts) / len(pts)
                    cy = sum(p[1] for p in pts) / len(pts)
                    new_centers.append((cx, cy))
                else:  # 如果该簇没有点，则保持原中心
                    new_centers.append(centers[cid])
            if centers == new_centers:
                break  # 当中心不再变化时，结束迭代
            centers = new_centers
        return labels, centers  # 如果k=3，则labels中的值为0, 1, 2?

    def _assign_noise_to_nearest_cluster(self, coordinates, labels):
        """
        将DBSCAN的噪声点分配给最近的簇（参考test_clustering_validation.py）
        """
        processed = labels[:]
        # 收集簇中心
        cluster_ids = [l for l in set(labels) if l >= 0]
        if not cluster_ids:
            return processed
        centers = {}
        for cid in cluster_ids:
            pts = [coordinates[i] for i, lab in enumerate(labels) if lab == cid]
            if pts:
                cx = sum(p[0] for p in pts) / len(pts)
                cy = sum(p[1] for p in pts) / len(pts)
                centers[cid] = (cx, cy)
        # 为噪声点分配最近簇
        for i, lab in enumerate(labels):
            if lab == -1:
                px, py = coordinates[i]
                best_cid = -1
                best_d = float('inf')
                for cid, c in centers.items():
                    d = math.sqrt((px - c[0])**2 + (py - c[1])**2)
                    if d < best_d:
                        best_d = d
                        best_cid = cid
                if best_cid != -1:
                    processed[i] = best_cid
        return processed

    def _estimate_clusters(self, positions):
        """
        自动估计聚类数量

        聚类数量确定原理：
        1. 少量目标（≤10）：使用固定规则
        2. 大量目标（>10）：使用肘部法则(Elbow Method)
        3. 肘部法则：计算不同k值的聚类内平方和(inertia)，寻找下降幅度突然变缓的点
        4. 使用二阶差分找到肘部点，即最佳聚类数

        Args:
            positions: 目标位置数组

        Returns:
            int: 估计的最佳聚类数量
        """
        n_points = len(positions)
        print(f"开始估计聚类数量，目标总数: {n_points}")

        if n_points <= 2:
            print("目标数量≤2，设置为1个簇")
            return 1
        elif n_points <= 5:
            print("目标数量≤5，设置为2个簇")
            return 2
        elif n_points <= 10:
            print("目标数量≤10，设置为3个簇")
            return 3
        else:
            # 使用肘部法则估计最佳聚类数
            max_k = min(8, n_points // 2)  # 最大聚类数不超过8或目标数的一半
            inertias = []
            print(f"使用肘部法则，测试1到{max_k}个簇")

            for k in range(1, max_k + 1):
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                kmeans.fit(positions)
                inertias.append(kmeans.inertia_)
                print(f"k={k}, inertia={kmeans.inertia_:.2f}")

            # 寻找肘部点
            if len(inertias) >= 3:
                # 计算二阶差分
                second_diffs = []
                for i in range(1, len(inertias) - 1):
                    second_diff = inertias[i-1] - 2*inertias[i] + inertias[i+1]
                    second_diffs.append(second_diff)
                    print(f"二阶差分[{i+1}]: {second_diff:.2f}")

                if second_diffs:
                    elbow_idx = np.argmax(second_diffs) + 2  # +2因为从索引1开始
                    optimal_k = min(elbow_idx, max_k)
                    print(f"肘部法则确定最佳聚类数: {optimal_k}")
                    return optimal_k

            # 如果肘部法则失败，使用默认值
            default_k = min(4, max_k)
            print(f"肘部法则失败，使用默认聚类数: {default_k}")
            return default_k

    def _generate_cluster_colors(self, n_clusters):
        """生成聚类颜色"""
        colors = []
        for i in range(n_clusters):  # 有多少个簇就有多少个颜色
            # 使用HSV色彩空间生成不同的颜色
            hue = (i * 360 // n_clusters) % 360
            # 转换为RGB
            import colorsys
            rgb = colorsys.hsv_to_rgb(hue/360.0, 0.8, 0.9)
            color = tuple(int(c * 255) for c in rgb)
            colors.append(color)

        return colors

    def visualize_clusters(self, image, targets_info, cluster_result):
        """
        在图像上可视化聚类结果

        Args:
            image: 原始图像
            targets_info: 目标信息列表
            cluster_result: 聚类结果

        Returns:
            np.ndarray: 带有聚类可视化的图像
        """
        result_image = image.copy()

        # 修复数组判断问题
        cluster_labels = cluster_result.get('cluster_labels', [])
        if len(cluster_labels) == 0:
            return result_image

        cluster_labels = cluster_result.get('cluster_labels', [])
        cluster_colors = cluster_result.get('cluster_colors', [])
        valid_indices = cluster_result.get('valid_indices', list(range(len(targets_info))))

        # 绘制每个目标点和其聚类颜色
        for i, (target_idx, cluster_label) in enumerate(zip(valid_indices, cluster_labels)):
            # 检查索引是否有效
            if target_idx >= len(targets_info):
                continue
            target = targets_info[target_idx]
            if 'pixel_x' in target and 'pixel_y' in target:
                x, y = int(target['pixel_x']), int(target['pixel_y'])
                color = cluster_colors[cluster_label % len(cluster_colors)]

                # 绘制目标点
                cv2.circle(result_image, (x, y), 8, color, -1)
                cv2.circle(result_image, (x, y), 10, (255, 255, 255), 2)

                # 绘制聚类标签
                cv2.putText(result_image, f'C{cluster_label}',
                           (x + 15, y - 15), cv2.FONT_HERSHEY_SIMPLEX,
                           0.4, color, 1)

        # # 绘制聚类中心
        # for i, center in enumerate(cluster_result['cluster_centers']):
        #     center_x, center_y = int(center[0]), int(center[1])
        #     color = cluster_colors[i % len(cluster_colors)]

        #     # 绘制聚类中心
        #     cv2.drawMarker(result_image, (center_x, center_y), color,
        #                   cv2.MARKER_CROSS, 20, 3)

        #     # 绘制聚类大小信息（使用英文缩写）
        #     size = cluster_result['cluster_sizes'][i] if i < len(cluster_result['cluster_sizes']) else 0
        #     cv2.putText(result_image, f'C{i}:{size}T',
        #                (center_x + 25, center_y + 25), cv2.FONT_HERSHEY_SIMPLEX,
        #                0.4, color, 1)

        return result_image

# ==================== 法线计算和保存功能模块 ====================

class NormalMapProcessor:
    """法线图处理器"""

    def __init__(self, model_path="prs-eth/marigold-normals-lcm-v0-1", device="cuda"):
        """
        初始化法线图处理器

        Args:
            model_path: Marigold模型路径
            device: 计算设备
        """
        self.model_path = model_path
        self.device = device
        self.pipeline = None
        self._load_model()

    def _load_model(self):
        """加载Marigold模型"""
        try:
            from diffusers import MarigoldNormalsPipeline
            self.pipeline = MarigoldNormalsPipeline.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,
                variant="fp16"
            )
            self.pipeline = self.pipeline.to(self.device)
            print(f"Marigold法线模型已加载: {self.model_path}")
        except Exception as e:
            print(f"加载Marigold模型失败: {e}")
            self.pipeline = None

    def calculate_normal_map(self, image, save_path=None):
        """
        计算法线图

        Args:
            image: 输入RGB图像 (numpy array)
            save_path: 保存路径（可选）

        Returns:
            torch.Tensor: 法线图张量，形状为 (H, W, 3)
        """
        if self.pipeline is None:
            print("Marigold模型未加载，无法计算法线图")
            return None

        try:
            # 确保图像是PIL格式
            if isinstance(image, np.ndarray):
                from PIL import Image
                if image.dtype != np.uint8:
                    image = (image * 255).astype(np.uint8)
                pil_image = Image.fromarray(image)
            else:
                pil_image = image

            # 计算法线图
            normal_map = self.pipeline(
                pil_image,
                processing_resolution=768,
                num_inference_steps=1,
                ensemble_size=1
            ).prediction

            # 保存法线图
            if save_path:
                self.save_normal_map(normal_map, save_path)

            return normal_map

        except Exception as e:
            print(f"计算法线图失败: {e}")
            return None

    def save_normal_map(self, normal_map, save_path):
        """
        保存法线图

        Args:
            normal_map: 法线图张量
            save_path: 保存路径
        """
        try:
            if isinstance(normal_map, torch.Tensor):
                # 转换为numpy数组
                normal_np = normal_map.cpu().numpy()
            else:
                normal_np = normal_map

            # 确保值在[0, 1]范围内
            normal_np = np.clip(normal_np, 0, 1)

            # 转换为uint8格式
            normal_uint8 = (normal_np * 255).astype(np.uint8)

            # 保存图像
            cv2.imwrite(save_path, cv2.cvtColor(normal_uint8, cv2.COLOR_RGB2BGR))
            print(f"法线图已保存: {save_path}")

        except Exception as e:
            print(f"保存法线图失败: {e}")

    def visualize_normal_map(self, normal_map, title="Normal Map"):
        """
        可视化法线图

        Args:
            normal_map: 法线图张量
            title: 图像标题
        """
        try:
            if isinstance(normal_map, torch.Tensor):
                normal_np = normal_map.cpu().numpy()
            else:
                normal_np = normal_map

            plt.figure(figsize=(10, 8))
            plt.imshow(normal_np)
            plt.title(title)
            plt.axis('off')
            plt.tight_layout()
            plt.show()

        except Exception as e:
            print(f"可视化法线图失败: {e}")

    def is_available(self):
        """检查法线计算功能是否可用"""
        return self.pipeline is not None

# ==================== 参数控制系统 ====================

class StrategyController:
    """策略控制器"""

    def __init__(self, strategy_params=None):
        """
        初始化策略控制器

        Args:
            strategy_params: 策略参数列表 [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]
                           例如: [1, 2, 3, 1, 2]
                           - pick_strategy: 1=几何中心, 2=最高点, 3=最缓点
                           - size_strategy: 1=最长线段, 2=星型线段, 3=圆形拟合
                           - order_strategy: 1=按深度, 2=凸包算法, 3=圆度排序, 4=占用图排序
                           - clustering: 1=开启聚类, 2=不聚类
                           - normal_calc: 1=计算法线, 2=不计算法线
        """
        self.strategy_params = strategy_params or [1, 1, 1, 2, 2]  # 默认策略，即第4版本代码策略
        self.validate_params()

        # 初始化各个模块
        self.normal_processor = None
        self.clustering = None

        # 根据参数初始化模块
        self._initialize_modules()

    def validate_params(self):
        """验证参数有效性"""
        if len(self.strategy_params) != 5:
            raise ValueError("策略参数必须包含5个元素: [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]")

        pick_strategy, size_strategy, order_strategy, clustering, normal_calc = self.strategy_params

        if pick_strategy not in [1, 2, 3]:
            raise ValueError("采摘点策略空间是1(几何中心), 2(最高点), 或3(最缓点)")

        if size_strategy not in [1, 2, 3]:
            raise ValueError("尺寸策略空间是1(最长线段), 2(星型线段), 或3(圆形拟合)")

        if order_strategy not in [1, 2, 3, 4]:
            raise ValueError("顺序策略空间是1(深度排序), 2(凸包排序), 3(圆度排序), 或4(占用图排序)")

        if clustering not in [1, 2, 3]:
            raise ValueError("聚类选项是1(DBSCAN), 2(K-means), 或3(关闭)")

        if normal_calc not in [1, 2]:
            raise ValueError("法线计算选项是1(开启)或2(关闭)")

    def _initialize_modules(self):
        """根据参数初始化模块"""
        pick_strategy, size_strategy, order_strategy, clustering, normal_calc = self.strategy_params

        # 初始化法线处理器
        if normal_calc == 1:
            self.normal_processor = NormalMapProcessor()

        # 初始化聚类器【250813】
        # clustering: 1=DBSCAN, 2=K-means, 3=关闭
        if clustering in [1, 2]:
            self.clustering = PositionClustering(
                n_clusters=None,  # 默认为None，自动确定
                max_distance=200,  # default eps; can be tuned externally
                clustering_method=clustering
            )
        else:
            self.clustering = PositionClustering(clustering_method=3)

    def get_strategy_description(self):
        """获取当前策略组合的描述"""
        pick_strategy, size_strategy, order_strategy, clustering, normal_calc = self.strategy_params

        pick_desc = {1: "几何中心", 2: "最高点", 3: "最缓点"}[pick_strategy]
        size_desc = {1: "最长线段", 2: "星型线段", 3: "圆形拟合"}[size_strategy]
        order_desc = {1: "按深度排序", 2: "凸包算法", 3: "圆度排序", 4: "占用图排序"}[order_strategy]  # 【250925】
        cluster_desc = {1: "DBSCAN聚类", 2: "K-means聚类", 3: "关闭聚类"}[clustering]  # 【250813】
        normal_desc = {1: "计算法线", 2: "不计算法线"}[normal_calc]

        return {
            "picking_point": pick_desc,
            "size_estimation": size_desc,
            "sequence_planning": order_desc,
            "clustering": cluster_desc,
            "normal_calculation": normal_desc,
            "full_description": f"采摘点:{pick_desc} | 尺寸:{size_desc} | 顺序:{order_desc} | 聚类:{cluster_desc} | 法线:{normal_desc}"
        }

    def process_targets(self, image, masks, depth_frame, depth_intrin, SIZEBIAS=0, save_normal_path=None):
        """
        处理所有目标

        Args:
            image: RGB图像
            masks: mask列表
            depth_frame: 深度帧
            depth_intrin: 深度相机内参
            SIZEBIAS: 尺寸偏差
            save_normal_path: 法线图保存路径

        Returns:
            dict: 处理结果
        """
        pick_strategy, size_strategy, order_strategy, clustering, normal_calc = self.strategy_params

        # 计算法线图（如果需要）
        normal_map = None
        marigold_time = 0.0
        if normal_calc == 1 and self.normal_processor and self.normal_processor.is_available():
            marigold_start = time.time()
            normal_map = self.normal_processor.calculate_normal_map(image, save_normal_path)
            marigold_time = time.time() - marigold_start

        # 获取策略实例
        picking_strategy = get_picking_point_strategy(pick_strategy)
        size_strategy_obj = get_size_estimation_strategy(size_strategy)
        sequence_strategy = get_sequence_planning_strategy(order_strategy)

        # 处理每个目标
        targets_info = []
        for i, mask in enumerate(masks):
            # 转换mask格式
            if isinstance(mask, torch.Tensor):
                mask_np = mask.cpu().numpy().astype(bool)
            else:
                mask_np = mask.astype(bool)

            # 应用mask偏移校正（参考第4版）
            mask_np = apply_mask_offset(mask_np, OFFSETX, OFFSETY)

            # 获取采摘点位
            pixel_x, pixel_y, world_point, pick_info = picking_strategy.get_picking_point(
                mask_np, depth_frame, depth_intrin, normal_map
            )

            if pixel_x is None:
                continue

            # 估算尺寸
            size, measurement_points, size_info = size_strategy_obj.estimate_size(
                mask_np, depth_frame, depth_intrin, SIZEBIAS
            )

            if size is None:
                continue

            # 保存目标信息
            target_info = {
                'index': i,
                'mask': mask_np,
                'pixel_x': pixel_x,
                'pixel_y': pixel_y,
                'world_point': world_point,
                'size': size,
                'measurement_points': measurement_points,
                'pick_info': pick_info,
                'size_info': size_info,
                'mask_info': {'mask': mask_np}  # 为占用图策略提供mask信息
            }
            targets_info.append(target_info)

        # 聚类处理（始终先执行聚类，再进行簇内顺序规划）
        print("开始进行目标聚类...")
        cluster_result = self.clustering.cluster_targets(targets_info) if self.clustering else {
            'cluster_labels': [0] * len(targets_info),
            'cluster_colors': [(255, 0, 0)],
            'sorted_clusters': [0],
            'valid_indices': list(range(len(targets_info)))
        }
        print(f"聚类完成，共{len(set(cluster_result.get('cluster_labels', [])))}个簇")

        # 将簇标签写入每个目标，便于后续可视化与处理
        cluster_labels = cluster_result.get('cluster_labels', [])
        valid_indices = cluster_result.get('valid_indices', list(range(len(targets_info))))
        for t_idx, c_label in zip(valid_indices, cluster_labels):
            if 0 <= t_idx < len(targets_info):
                targets_info[t_idx]['cluster_label'] = c_label

        # 当仅有一个簇时，其行为等价于非聚类的顺序规划
        # 多簇时按sorted_clusters顺序逐簇执行选定的顺序策略，再合并为最终序列
        print(f"开始规划采摘顺序（簇内策略：{order_strategy}）")
        final_indices = []
        sorted_clusters = cluster_result.get('sorted_clusters', [])
        if not sorted_clusters:
            # 如果意外没有sorted_clusters，回退为单簇
            sorted_clusters = [0]
        for cid in sorted_clusters:
            # 找出属于该簇的目标在原targets_info中的索引
            cluster_target_indices = [i for i, t in enumerate(targets_info) if t.get('cluster_label', 0) == cid]
            if not cluster_target_indices:
                continue
            # 针对该簇内目标执行顺序规划
            sub_targets = [targets_info[i] for i in cluster_target_indices]
            sub_order = sequence_strategy.plan_sequence(sub_targets)
            # 将簇内相对索引映射回原索引
            final_indices.extend([cluster_target_indices[i] for i in sub_order if i < len(cluster_target_indices)])

        # 重新排序目标信息（最终簇感知的采摘顺序）
        ordered_targets = [targets_info[i] for i in final_indices if i < len(targets_info)]

        # 为排序后的目标添加采摘序号
        for i, target in enumerate(ordered_targets):
            target['pick_order'] = i + 1  # 从1开始编号

        print(f"最终确定采摘顺序：共{len(ordered_targets)}个目标")

        return {
            'targets_info': ordered_targets,
            'cluster_result': cluster_result,
            'normal_map': normal_map,
            'strategy_description': self.get_strategy_description(),
            'total_targets': len(ordered_targets),
            'marigold_time': marigold_time
        }

# ==================== 性能监控和数据记录功能 ====================

class PerformanceLogger:
    """性能监控和数据记录器"""

    def __init__(self, log_dir="logs"):
        """
        初始化性能记录器

        Args:
            log_dir: 日志保存目录
        """
        self.log_dir = log_dir
        self.session_data = []
        self.current_record = None  # 初始化为None
        self.session_start_time = None  # 初始化为None
        self.csv_filename = None  # 用于存储CSV文件名
        self.first_image_time = None  # 记录第一张图的时间

        # 创建日志目录
        import os
        os.makedirs(log_dir, exist_ok=True)

        # 初始化DataFrame（如果pandas可用）
        self.columns = [
            'timestamp', 'imgname', 'yolo_latency', 'marigold_latency',
            'postprocess_latency', 'total_latency', 'target_count',
            'strategy_params', 'target_info'
        ]
        if pd is not None:
            self.df = pd.DataFrame(columns=self.columns)
        else:
            self.df = None
            print("警告: pandas不可用，将使用备用数据记录方式")

    def start_session(self, imgname):
        """开始一个新的处理会话"""
        print(f"开始新的性能记录会话: {imgname}")

        # 如果是第一张图，设置CSV文件名
        if self.first_image_time is None:
            # 从图像名称中提取时间戳来命名CSV文件
            self.first_image_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            # 尝试从图像名称中提取时间信息
            import re
            time_match = re.search(r'(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})', imgname)
            if time_match:
                year, month, day, hour, minute, second = time_match.groups()
                self.first_image_time = f"{year}{month}{day}_{hour}{minute}{second}"

            self.csv_filename = f"vision_performance_{self.first_image_time}.csv"
            print(f"设置CSV文件名: {self.csv_filename}")

        self.current_record = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'imgname': imgname,
            'yolo_latency': 0.0,
            'marigold_latency': 0.0,
            'postprocess_latency': 0.0,
            'total_latency': 0.0,
            'target_count': 0,
            'strategy_params': '',
            'target_info': ''
        }
        self.session_start_time = time.time()

        print(f"会话开始时间: {self.session_start_time}")
        print(f"当前记录: {self.current_record['imgname']}")

    def record_yolo_time(self, latency):
        """记录YOLO推理时间"""
        if self.current_record is not None:
            self.current_record['yolo_latency'] = latency
            print(f"记录YOLO推理时间: {latency:.3f}s")
        else:
            print("警告: 尝试记录YOLO时间但current_record不存在，需要先调用start_session")

    def record_marigold_time(self, latency):
        """记录Marigold推理时间"""
        if self.current_record is not None:
            self.current_record['marigold_latency'] = latency
            print(f"记录Marigold推理时间: {latency:.3f}s")
        else:
            print("警告: 尝试记录Marigold时间但current_record不存在，需要先调用start_session")

    def record_postprocess_time(self, latency):
        """记录后处理时间"""
        if self.current_record is not None:
            self.current_record['postprocess_latency'] = latency
            print(f"记录后处理时间: {latency:.3f}s")
        else:
            print("警告: 尝试记录后处理时间但current_record不存在，需要先调用start_session")

    def end_session(self, target_info, strategy_params):
        """结束处理会话并保存记录"""
        print(f"结束性能记录会话，目标数量: {len(target_info) if target_info else 0}")

        # 确保session_start_time存在
        if self.session_start_time is None:
            print("错误: session_start_time不存在，这表明start_session没有被正确调用")
            return {}

        # 确保current_record存在
        if self.current_record is None:
            print("错误: current_record不存在，这表明start_session没有被正确调用")
            return {}

        current_time = time.time()
        self.current_record['total_latency'] = current_time - self.session_start_time

        print(f"会话总时长: {self.current_record['total_latency']:.3f}s")
        print(f"YOLO时间: {self.current_record['yolo_latency']:.3f}s")
        print(f"Marigold时间: {self.current_record['marigold_latency']:.3f}s")
        print(f"后处理时间: {self.current_record['postprocess_latency']:.3f}s")

        self.current_record['target_count'] = len(target_info) if target_info else 0
        self.current_record['strategy_params'] = str(strategy_params)

        # 简化目标信息用于记录（移除pick_type和size_type，因为已有strategy_params）
        # 注意：target_info中的目标顺序就是经过顺序规划后的最终采摘顺序
        simplified_targets = []
        for i, target in enumerate(target_info):
            # 提取采摘点像素坐标
            pixel_x = target.get('pixel_x', 0)
            pixel_y = target.get('pixel_y', 0)

            # 提取真实三维坐标
            world_point = target.get('world_point', [0, 0, 0])

            # 提取尺寸信息
            size = target.get('size', 0)

            # 提取法线信息（如果存在）
            pick_info = target.get('pick_info', {})
            normal = pick_info.get('normal', [0, 0, 1]) if 'normal' in pick_info else [0, 0, 1]

            # 提取采摘顺序
            pick_order = target.get('pick_order', i + 1)

            # 格式: [order,x,y,D,X,Y,Z,α,β,γ] - 在原有基础上添加采摘顺序
            simplified_target = [
                pick_order,   # order - 采摘顺序（1开始）
                pixel_x,      # x - 采摘点像素坐标
                pixel_y,      # y - 采摘点像素坐标
                size,         # D - 真实尺寸
                world_point[0],  # X - 真实三维坐标
                world_point[1],  # Y - 真实三维坐标
                world_point[2],  # Z - 真实三维坐标
                normal[0],    # α - 法线方向
                normal[1],    # β - 法线方向
                normal[2]     # γ - 法线方向
            ]
            simplified_targets.append(simplified_target)

        self.current_record['target_info'] = str(simplified_targets)

        # 添加到DataFrame或备用存储
        if self.df is not None:
            try:
                new_row = pd.DataFrame([self.current_record])
                self.df = pd.concat([self.df, new_row], ignore_index=True)
            except Exception as e:
                print(f"添加数据到DataFrame失败: {e}")
                # 备用存储方式
                self.session_data.append(self.current_record.copy())
        else:
            # 使用备用存储方式
            self.session_data.append(self.current_record.copy())

        # 每次处理完一张图片就保存到文件
        self.save_to_file()

        # 返回当前记录的副本
        result = self.current_record.copy()

        # 清理当前会话数据（但保持历史数据）
        self.current_record = None
        self.session_start_time = None

        return result

    def save_to_file(self):
        """保存数据到CSV文件"""
        # 使用预设的文件名，如果没有则使用当前时间
        if self.csv_filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.csv_filename = f"vision_performance_{timestamp}.csv"

        filepath = os.path.join(self.log_dir, self.csv_filename)

        try:
            if self.df is not None and not self.df.empty:
                # 使用pandas保存，追加模式
                self.df.to_csv(filepath, index=False, encoding='utf-8')
                print(f"性能数据已更新到CSV: {filepath}")
            elif self.session_data:
                # 使用备用方式保存
                import csv
                with open(filepath, 'w', newline='', encoding='utf-8') as f:
                    if self.session_data:
                        writer = csv.DictWriter(f, fieldnames=self.columns)
                        writer.writeheader()
                        writer.writerows(self.session_data)
                print(f"性能数据已更新到CSV（备用方式）: {filepath}")
            else:
                print("没有数据需要保存")
        except Exception as e:
            print(f"保存性能数据失败: {e}")
            # 尝试备用保存方式
            try:
                backup_filename = f"vision_performance_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                backup_filepath = os.path.join(self.log_dir, backup_filename)
                with open(backup_filepath, 'w', encoding='utf-8') as f:
                    if self.df is not None:
                        f.write(str(self.df.to_dict('records')))
                    else:
                        f.write(str(self.session_data))
                print(f"性能数据已保存到备用文件: {backup_filepath}")
            except Exception as e2:
                print(f"备用保存方式也失败: {e2}")

    def get_statistics(self):
        """获取性能统计信息"""
        data_source = None

        if self.df is not None and not self.df.empty:
            data_source = self.df
        elif self.session_data:
            # 从备用数据创建临时DataFrame
            if pd is not None:
                try:
                    data_source = pd.DataFrame(self.session_data)
                except:
                    pass

        if data_source is None or (hasattr(data_source, 'empty') and data_source.empty):
            return {}

        try:
            stats = {
                'total_sessions': len(data_source),
                'avg_yolo_latency': data_source['yolo_latency'].mean(),
                'avg_marigold_latency': data_source['marigold_latency'].mean(),
                'avg_postprocess_latency': data_source['postprocess_latency'].mean(),
                'avg_total_latency': data_source['total_latency'].mean(),
                'avg_target_count': data_source['target_count'].mean(),
                'max_total_latency': data_source['total_latency'].max(),
                'min_total_latency': data_source['total_latency'].min()
            }
        except Exception as e:
            print(f"计算统计信息失败: {e}")
            # 备用统计方式
            if self.session_data:
                total_sessions = len(self.session_data)
                yolo_times = [d.get('yolo_latency', 0) for d in self.session_data]
                total_times = [d.get('total_latency', 0) for d in self.session_data]
                target_counts = [d.get('target_count', 0) for d in self.session_data]

                stats = {
                    'total_sessions': total_sessions,
                    'avg_yolo_latency': sum(yolo_times) / len(yolo_times) if yolo_times else 0,
                    'avg_total_latency': sum(total_times) / len(total_times) if total_times else 0,
                    'avg_target_count': sum(target_counts) / len(target_counts) if target_counts else 0,
                    'max_total_latency': max(total_times) if total_times else 0,
                    'min_total_latency': min(total_times) if total_times else 0
                }
            else:
                stats = {}

        return stats

    def print_statistics(self):
        """打印性能统计信息"""
        stats = self.get_statistics()
        if not stats:
            print("暂无性能数据")
            return

        print("\n=== 性能统计信息 ===")
        print(f"总处理次数: {stats['total_sessions']}")
        print(f"平均YOLO推理时间: {stats['avg_yolo_latency']:.3f}s")
        print(f"平均Marigold推理时间: {stats['avg_marigold_latency']:.3f}s")
        print(f"平均后处理时间: {stats['avg_postprocess_latency']:.3f}s")
        print(f"平均总处理时间: {stats['avg_total_latency']:.3f}s")
        print(f"平均目标数量: {stats['avg_target_count']:.1f}")
        print(f"最长处理时间: {stats['max_total_latency']:.3f}s")
        print(f"最短处理时间: {stats['min_total_latency']:.3f}s")
        print("==================\n")

# 时间测量装饰器
def measure_time(func):
    """时间测量装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        latency = end_time - start_time
        return result, latency
    return wrapper

# ==================== 可视化辅助函数 ====================

def draw_masks_on_image(image, targets_info, cluster_result=None):
    """
    在图像上绘制mask轮廓

    Args:
        image: RGB图像
        targets_info: 目标信息列表
        cluster_result: 聚类结果（可选）

    Returns:
        np.ndarray: 绘制了mask的图像
    """
    result_image = image.copy()

    # 创建一个单独的半透明层来累积所有mask
    overlay = np.zeros_like(result_image)

    # 获取聚类颜色列表；单簇时应为红色，由聚类模块保证
    colors_list = []
    if cluster_result and 'cluster_colors' in cluster_result:
        colors_list = cluster_result.get('cluster_colors', [])
    if not colors_list:
        colors_list = [(255, 0, 0)]

    # 绘制每个目标的mask轮廓和填充
    for i, target in enumerate(targets_info):
        if 'mask' not in target:
            continue

        mask = target['mask']

        # 确保mask是正确的格式
        if isinstance(mask, np.ndarray):
            if mask.dtype == bool:
                mask_uint8 = mask.astype(np.uint8) * 255
            else:
                mask_uint8 = mask.astype(np.uint8)
        else:
            continue

        # 找到轮廓
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 确定颜色：优先使用目标自身的cluster_label
        if 'cluster_label' in target and colors_list:
            color = colors_list[int(target['cluster_label']) % len(colors_list)]
        else:
            color = (255, 0, 0)

        # 先在overlay上绘制填充（不叠加）
        cv2.drawContours(overlay, contours, -1, color, -1)

        # 在原图上绘制轮廓（确保轮廓清晰可见）
        cv2.drawContours(result_image, contours, -1, color, 2)

    # 最后一次性将overlay与原图混合，避免重复叠加
    if np.any(overlay):
        result_image = cv2.addWeighted(result_image, 0.7, overlay, 0.3, 0)

    return result_image

# ==================== 第5版核心处理函数 ====================

def process_vision_v5(imgrgb, savePath, results, depth_frame, depth_intrin,
                     TSIZE, SIZEBIAS, strategy_params, imgname=None,
                     save_normal=False, logger=None):
    """
    第5版视觉系统核心处理函数

    Args:
        imgrgb: RGB图像
        savePath: 保存路径
        results: YOLOv8的检测结果（ultralytics 8.1.9 PASS；8.3.84 FAIL）
        depth_frame: 深度帧
        depth_intrin: 深度相机内参
        TSIZE: 目标尺寸阈值
        SIZEBIAS: 尺寸偏差
        strategy_params: 策略参数列表 [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]
        imgname: 图像名称
        save_normal: 是否保存法线图
        logger: 性能记录器

    Returns:
        tuple: (result_image, targets_info, processing_info)
    """

    # 性能记录（session应该已经在主程序中开始）
    # 不在这里重新开始session，避免重置已记录的数据

    postprocess_start = time.time()

    # 初始化策略控制器
    controller = StrategyController(strategy_params)

    # 提取masks
    if hasattr(results, 'masks') and results.masks is not None:
        masks = results.masks.data.cpu().numpy()
    else:
        print("未检测到任何目标")
        # 即使没有目标，也创建并保存可视化图像
        result_image = imgrgb.copy()

        # 添加"无目标"标识
        cv2.putText(result_image, "No targets detected", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        cv2.putText(result_image, "V5 Vision System", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)

        # 保存结果图像
        if savePath:
            cv2.imwrite(savePath, cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR))
            print(f"无目标结果图像已保存: {savePath}")

        # 记录后处理时间
        postprocess_time = time.time() - postprocess_start
        if logger:
            logger.record_postprocess_time(postprocess_time)
            processing_info = logger.end_session([], strategy_params)
        else:
            processing_info = {}

        return result_image, [], processing_info

    # 法线图保存路径
    normal_save_path = None
    if save_normal and savePath:
        normal_save_path = os.path.join(os.path.dirname(savePath),
                                       f"normal_{os.path.basename(savePath)}")

    # 处理所有目标
    processing_result = controller.process_targets(
        imgrgb, masks, depth_frame, depth_intrin,
        SIZEBIAS, normal_save_path
    )

    targets_info = processing_result['targets_info']
    cluster_result = processing_result['cluster_result']
    normal_map = processing_result['normal_map']
    strategy_description = processing_result['strategy_description']
    marigold_time = processing_result.get('marigold_time', 0.0)

    # 记录marigold时间
    if logger:
        logger.record_marigold_time(marigold_time)

    # 过滤尺寸不符合要求的目标
    filtered_targets = []
    for target in targets_info:
        if target['size'] >= TSIZE:
            filtered_targets.append(target)

    # 记录后处理时间
    postprocess_time = time.time() - postprocess_start
    if logger:
        logger.record_postprocess_time(postprocess_time)

    # 创建结果图像
    result_image = create_result_visualization(
        imgrgb, filtered_targets, cluster_result, strategy_description, strategy_params
    )

    # 保存结果图像
    if savePath:
        cv2.imwrite(savePath, cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR))
        print(f"结果图像已保存: {savePath}")

    # 结束性能记录
    processing_info = {}
    if logger:
        processing_info = logger.end_session(filtered_targets, strategy_params)

    # 打印处理结果
    print(f"\n=========================")
    print(f"策略组合: {strategy_description['full_description']}")
    print(f"检测到目标数量: {len(targets_info)}")
    print(f"符合尺寸要求的目标数量: {len(filtered_targets)}")
    print(f"后处理用时: {postprocess_time:.3f}s")
    if cluster_result:
        labels = cluster_result.get('cluster_labels', [])
        n_clusters = len(set(labels)) if labels else 0
        print(f"聚类数量: {n_clusters}")
    print("============================\n")

    return result_image, filtered_targets, processing_info

def create_result_visualization(image, targets_info, cluster_result=None, strategy_description=None, strategy_params=None):
    """
    创建结果可视化图像

    Args:
        image: 原始图像 (RGB格式)
        targets_info: 目标信息列表
        cluster_result: 聚类结果
        strategy_description: 策略描述
        strategy_params: 实际使用的策略参数 [pick, size, order, cluster, normal]

    Returns:
        np.ndarray: 可视化结果图像 (RGB格式)
    """
    # 确保图像是RGB格式的副本
    if len(image.shape) == 3 and image.shape[2] == 3:
        result_image = image.copy()
    else:
        result_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB) if image.shape[2] == 3 else image.copy()

    # 首先绘制所有mask轮廓
    result_image = draw_masks_on_image(result_image, targets_info, cluster_result)

    # # 如果有聚类结果，绘制聚类可视化
    # if cluster_result:
    #     clustering = PositionClustering()
    #     result_image = clustering.visualize_clusters(result_image, targets_info, cluster_result)

    # 绘制每个目标的详细信息
    for i, target in enumerate(targets_info):
        pixel_x = int(target['pixel_x'])
        pixel_y = int(target['pixel_y'])
        size = target['size']

        # 绘制采摘点
        cv2.circle(result_image, (pixel_x, pixel_y), 3, (0, 255, 0), -1)  # 颜色为绿色
        # cv2.circle(result_image, (pixel_x, pixel_y), 5, (255, 255, 255), 2)

        # 绘制尺寸测量点
        if 'measurement_points' in target and target['measurement_points']:
            for point in target['measurement_points']:
                cv2.circle(result_image, (int(point[0]), int(point[1])), 3, (255, 0, 0), -1)  # 颜色为蓝色

        # 绘制尺寸信息可视化
        if 'size_info' in target and target['size_info']:
            size_info = target['size_info']
            if size_info['type'] == 'longest_diameter' and 'diameter_line' in size_info:
                p1, p2 = size_info['diameter_line']
                cv2.line(result_image, (int(p1[0]), int(p1[1])), (int(p2[0]), int(p2[1])), (255, 0, 0), 1)  # 颜色为红色
            elif size_info['type'] == 'star_shaped' and 'lines' in size_info:
                for line in size_info['lines']:
                    p1, p2 = line
                    cv2.line(result_image, (int(p1[0]), int(p1[1])), (int(p2[0]), int(p2[1])), (255, 0, 0), 1)  # 颜色为红色
            elif size_info['type'] == 'circle_fitting' and 'center' in size_info and 'radius' in size_info:
                center = size_info['center']
                radius = int(size_info['radius'])
                cv2.circle(result_image, (int(center[0]), int(center[1])), radius, (255, 0, 0), 1)  # 颜色为红色

        # 添加文本信息
        cv2.putText(result_image, f'#{i+1}', (pixel_x - 10, pixel_y-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(result_image, f's{size*1000:.1f}', (pixel_x - 15, pixel_y+15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 添加真实深度值显示（以mm为单位）
        if 'world_point' in target and target['world_point'] is not None:
            depth_mm = target['world_point'][2] * 1000  # 转换为毫米
            cv2.putText(result_image, f'd{depth_mm:.0f}', (pixel_x + 5, pixel_y + 3),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 如果使用占用图策略，绘制最优点和箭头
        if 'optimal_point_pixel' in target:
            optimal_point = target['optimal_point_pixel']
            optimal_x, optimal_y = int(optimal_point[0]), int(optimal_point[1])

            # 绘制最优点
            cv2.circle(result_image, (optimal_x, optimal_y), 3, (255, 255, 255), -1)  # 白色圆点

            # 绘制从几何中心到最优点的箭头
            cv2.arrowedLine(result_image, (pixel_x, pixel_y), (optimal_x, optimal_y),
                           (0, 255, 255), 2, tipLength=0.3)  # 黄色箭头

            # 显示序号（如果有）
            if 'sequence_order' in target:
                seq_num = target['sequence_order']
                cv2.putText(result_image, f'#{seq_num}', (optimal_x + 10, optimal_y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)  # 黄色序号

        # # 添加策略类型信息（使用英文缩写）
        # if 'pick_info' in target and target['pick_info']:
        #     pick_type = target['pick_info'].get('type', 'unknown')
        #     type_abbr = {
        #         'geometric_center': 'GC',
        #         'highest_point': 'HP',
        #         'safest_point': 'SP',
        #         'unknown': 'UK'
        #     }.get(pick_type, pick_type[:2].upper())
        #     cv2.putText(result_image, type_abbr, (pixel_x + 10, text_y + 35),
        #                cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

    # 在图像左上角注释策略信息
    if strategy_description:
        # 创建简化的英文策略描述
        pick_desc = {1: "GC", 2: "HP", 3: "SP"}  # Geometric Center, Highest Point, Safest Point
        size_desc = {1: "LD", 2: "SS", 3: "CF"}  # Longest Diameter, Star Shaped, Circle Fitting
        order_desc = {1: "DS", 2: "CH", 3: "CS"}  # Depth Sort, Convex Hull, Circularity Sort
        cluster_desc = {1: "CL+", 2: "CL-"}  # Clustering On/Off
        normal_desc = {1: "NM+", 2: "NM-"}  # Normal Map On/Off

        # 使用实际的策略参数
        try:
            # 优先使用传入的strategy_params，否则使用默认值
            if strategy_params and len(strategy_params) >= 5:
                params = strategy_params
            elif hasattr(strategy_description, 'strategy_params'):
                params = strategy_description.strategy_params
            else:
                params = [1, 1, 1, 2, 2]  # 默认值

            info_text = f"P:{pick_desc.get(params[0], 'UK')} S:{size_desc.get(params[1], 'UK')} O:{order_desc.get(params[2], 'UK')} {cluster_desc.get(params[3], 'UK')} {normal_desc.get(params[4], 'UK')}"
            cv2.putText(result_image, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        except:
            # 如果无法获取参数，显示基本信息
            cv2.putText(result_image, "V5 Vision System", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    return result_image


def find_mask_contours(mask):
    """找到mask的轮廓"""
    mask_uint8 = mask.astype(np.uint8) * 255
    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:  # 如果没有找到轮廓
        return [], (0, 0)

    # 找到最大的轮廓
    max_contour = max(contours, key=cv2.contourArea)

    # 计算最大轮廓的几何中心
    M = cv2.moments(max_contour)
    if M["m00"] > 0:  # 确保轮廓的面积大于0
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        center = (cx, cy)
    else:
        center = (0, 0)

    return contours, center

def find_mask_diameter(mask, sample_ratio=0.5):
    """
    寻找经过mask几何中心且长度最大的直径

    Args:
        mask: 2D布尔数组，表示mask区域

    Returns:
        start_point: 直径的起点坐标 (x, y)
        end_point: 直径的终点坐标 (x, y)
    """
    # 确保mask是布尔类型
    mask = mask.astype(np.uint8)

    # 计算mask的几何中心（质心）
    M = cv2.moments(mask)
    if M["m00"] == 0:
        return None, None  # 空mask
    center_x = int(M["m10"] / M["m00"])
    center_y = int(M["m01"] / M["m00"])
    center = (center_x, center_y)

    # 找到mask的轮廓
    contours, _ = find_mask_contours(mask)
    contours = [max(contours, key=cv2.contourArea)]
    if not contours:
        return None, None

    # 合并所有轮廓点并按固定间隔采样以减少计算量
    all_contour_points = []
    for contour in contours:
        # 计算采样步长，确保至少取2个点
        step = max(1, int(1 / sample_ratio))
        sampled_points = [point[0] for point in contour[::step]]
        all_contour_points.extend(sampled_points)

    # 如果轮廓点太少，返回None
    if len(all_contour_points) < 2:
        return None, None

    # 寻找经过或接近中心的最长线段
    max_distance = 0
    best_pair = None

    # 设置接近中心的阈值（线到中心点的最大距离）
    threshold = max(5, np.sqrt(mask.shape[0]**2 + mask.shape[1]**2) * 0.05)  # 5或图像对角线的5%

    # 对每一对轮廓点检查
    n = len(all_contour_points)
    for i in range(n):
        for j in range(i+1, n):
            p1 = all_contour_points[i]
            p2 = all_contour_points[j]

            # 计算点到线的距离
            # 线段方程: (x-x1)/(x2-x1) = (y-y1)/(y2-y1)
            # 距离公式: |Ax + By + C|/sqrt(A^2 + B^2)，其中线的方程为Ax + By + C = 0

            if p1[0] == p2[0]:  # 垂直线
                distance_to_center = abs(center_x - p1[0])
            elif p1[1] == p2[1]:  # 水平线
                distance_to_center = abs(center_y - p1[1])
            else:
                # 一般情况
                A = (p2[1] - p1[1])
                B = (p1[0] - p2[0])
                C = (p2[0]*p1[1] - p1[0]*p2[1])
                distance_to_center = abs(A*center_x + B*center_y + C) / np.sqrt(A**2 + B**2)

            # 如果线段经过或接近中心，计算长度
            if distance_to_center <= threshold:
                # 计算线段长度
                distance = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

                if distance > max_distance:
                    max_distance = distance
                    best_pair = (p1, p2)

    if best_pair is None:
        return None, None, None, None, None

    p1, p2 = best_pair

    # 计算三等分点
    # 使用参数方程：P = P1 + t(P2-P1)，其中t分别为1/3和2/3
    third_point1 = (
        int(p1[0] + (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + (p2[1] - p1[1]) / 3)   # y坐标
    )

    third_point2 = (
        int(p1[0] + 2 * (p2[0] - p1[0]) / 3),  # x坐标
        int(p1[1] + 2 * (p2[1] - p1[1]) / 3)   # y坐标
    )

    return best_pair[0], best_pair[1], center, third_point1, third_point2, contours

# 处理yolo8-seg输出mask的固有偏移
def apply_mask_offset(mask, offset_x, offset_y):
    """
    对mask应用偏移校正

    Args:
        mask: 2D布尔数组或二值数组，表示mask区域
        offset_x: x方向偏移量（负值向左，正值向右）
        offset_y: y方向偏移量（负值向上，正值向下）

    Returns:
        offset_mask: 偏移后的mask
    """
    if offset_x == 0 and offset_y == 0:
        return mask

    h, w = mask.shape
    offset_mask = np.zeros_like(mask)

    # 使用numpy切片实现高效偏移
    # 计算有效的源区域和目标区域
    if offset_y < 0:  # 向上移动
        src_y_slice = slice(-offset_y, None)
        dst_y_slice = slice(0, h + offset_y)
    elif offset_y > 0:  # 向下移动
        src_y_slice = slice(0, h - offset_y)
        dst_y_slice = slice(offset_y, None)
    else:  # 不移动
        src_y_slice = slice(None)
        dst_y_slice = slice(None)

    if offset_x < 0:  # 向左移动
        src_x_slice = slice(-offset_x, None)
        dst_x_slice = slice(0, w + offset_x)
    elif offset_x > 0:  # 向右移动
        src_x_slice = slice(0, w - offset_x)
        dst_x_slice = slice(offset_x, None)
    else:  # 不移动
        src_x_slice = slice(None)
        dst_x_slice = slice(None)

    # 执行偏移复制
    offset_mask[dst_y_slice, dst_x_slice] = mask[src_y_slice, src_x_slice]

    return offset_mask

# 【250402】采摘点计算，坐标排序与路径规划
def pickPoints(imgrgb, savePath, results, depth_frame, depth_intrin, TSIZE, SIZEBIAS, serial_n):
    '''
    # Segmentation
    result.masks.data      # masks, (N, H, W)
    result.masks.xy        # x,y segments (pixels), List[segment] * N
    result.masks.xyn       # x,y segments (normalized), List[segment] * N
    '''
    # normal_map = np.array(normals)
    # print('>>>>>>>>>>>>> results: ', results)
    masks = results.masks.data if results.masks is not None else []

    # 【250708】debug
    # print('>>>>>>>>>>>>> masks shape: ', masks[0].shape)
    # print('>>>>>>>>>>>>> rgb shape  : ', imgrgb.shape)

    detections = results.boxes.xywh
    print("-----Detected {:d} mushroom in image-----\n".format(detections.size()[0]))
    dets = []
    minus = [0, 0, 0, 0]  # pickable, pixel size, w/h ratio, real size

    # 创建结果图像
    result_image = imgrgb.copy()

    # 创建热力图颜色映射
    colormap = matplotlib.colormaps.get_cmap('jet')

    # 对每个检测到的实例进行处理
    for i, mask_tensor in enumerate(masks):

        # get info of detections
        detection = detections[i]

        # # 基于像素的长宽比判断
        # if detection[2]/detection[3]>1.3 or detection[2]/detection[3]<0.7:
        #     minus[2] += 1
        #     continue

        # Process pick points now
        # 转换为numpy数组
        mask = mask_tensor.cpu().numpy()
        # mask = cv2.resize(mask, (RSLUX, RSLUY), interpolation=cv2.INTER_NEAREST)
        # print('>>>>>>>>>>>>> masks shape: ', mask.shape)
        mask = apply_mask_offset(mask, OFFSETX, OFFSETY)

        # 找到mask的质心和经过几何中心的最长直径
        start_point, end_point, center, diameterP1, diameterP2, contours = find_mask_diameter(mask)
        cv2.drawContours(result_image, contours, -1, (0, 0, 255), 2)
        if start_point is not None and end_point is not None:
            # 顺便把几何中心绘制出来
            cv2.circle(result_image, center, 3, (0, 0, 255), -1)  # 红色实心点
            # 绘制直径线段（白色，粗细为2）
            cv2.line(result_image, start_point, end_point, (255, 255, 255), 1)
            # 把两个测量点绘制出来
            cv2.circle(result_image, diameterP1, 3, (0, 255, 0), -1)  # 绿色实心点
            cv2.circle(result_image, diameterP2, 3, (0, 255, 0), -1)  # 绿色实心点

        # 检测采摘点的深度值是否存在判断
        pick_depth = depth_frame.get_distance(int(center[0]), int(center[1]))
        if pick_depth == 0:  # 如果深度信息为空
            print('=====NULL depth info')
            minus[0] += 1
            continue
            # print('=====NULL depth info at ({:.0f},{:.0f}) and set to 0.245====='.format(safe_x.cpu().numpy(), safe_y.cpu().numpy()))
            # pick_depth = 0.2450

        rf1_depth = depth_frame.get_distance(int(diameterP1[0]), int(diameterP1[1]))
        rf2_depth = depth_frame.get_distance(int(diameterP2[0]), int(diameterP2[1]))
        rf1_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP1[0]), int(diameterP1[1])], rf1_depth)
        rf2_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(diameterP2[0]), int(diameterP2[1])], rf2_depth)

        # calculate the diameter of two rf points
        size = math.sqrt(sum((a-b) ** 2 for a, b in zip(rf1_point, rf2_point))) *3 + SIZEBIAS

        if size < TSIZE:
            print('=====Diameter too small: {:.1f}\n'.format(size*1000))
            minus[3] += 1
            continue
        print('-----Diameter: {:.1f}\n'.format(size*1000))
        # put size text
        labelsize = f"s{size*1000:.1f}"
        possize = (int(center[0]-20), int(center[1]+22))
        cv2.putText(result_image, labelsize,
                    possize,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 255, 0),  # 绿色指示尺寸
                    1)  # line type

        # 获取采摘点的深度坐标 picking point!
        pick_point = rs.rs2_deproject_pixel_to_point(depth_intrin, [int(center[0]), int(center[1])], pick_depth)
        # put depth text
        labeldepth = f"d{pick_point[2]*1000:.1f}"
        posdepth = (int(center[0]-20), int(center[1]-3))
        cv2.putText(result_image, labeldepth,
                    posdepth,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 0, 255),  # 红色指示深度
                    1)  # line type

        # grab useful info from processed results
        dets.append([pick_point[0], pick_point[1], pick_point[2], 0.0, 0.0, 0.0, size])

    print('=====Depreciated Depth holl, Pixel size, W/H ratio, Real size: ', minus)
    # 按照位置信息从上到下和从下到上
    aft_dets = sorted(dets, key=lambda d: d[2])  # 按照top值从小到大排序
    print('-----Output {} objects in image'.format(len(aft_dets)))

    cv2.imwrite(savePath, result_image)

    return aft_dets